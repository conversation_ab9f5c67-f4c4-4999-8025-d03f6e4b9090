-- commands.lua - Command functions for Air780EG
local commands = {}

-- Import required modules
local vars = require('variables')
local PinModule = require('pin_module')

-- Try to load MQTT module (optional)
local mqtt_module_available, mqtt_module = pcall(require, "mqtt_module")
if not mqtt_module_available then
    mqtt_module = {
        publish = function() return false end,
        is_ready = function() return false end,
        get_client_id = function() return "unknown" end
    }
end

-- Simple beep function for Air780
local function playBeep(sound_type)
    if vars.sound_flag then
        print("Playing beep:", sound_type)
        -- Use the PinModule beep function
        if PinModule.beep then
            if sound_type == "as" then
                PinModule.beep(2000, 300)  -- 2kHz for 300ms
            elseif sound_type == "lock" then
                PinModule.beep(1500, 200)  -- 1.5kHz for 200ms
            elseif sound_type == "unlock" then
                PinModule.beep(1000, 200)  -- 1kHz for 200ms
            elseif sound_type == "check" then
                PinModule.beep(1200, 150)  -- 1.2kHz for 150ms
            elseif sound_type == "untar" then
                PinModule.beep(800, 400)   -- 800Hz for 400ms
            else
                PinModule.beep(1000, 200)  -- Default beep
            end
        end
    end
end

function commands.checkCommand()
    print("Check command executed")
    playBeep("check")
    -- This function is mainly for MQTT/SMS acknowledgment
    -- The actual sensor data publishing is handled by the calling function
end

function commands.lockCommand()
    if not vars.isLicensed then 
        print("Device not licensed. Cannot execute lockCommand")
        return 
    end
    
    -- Initialize key power
    PinModule.relayControl("KeyPower", 1)
    
    sys.taskInit(function()
        print("Lock command executing")
        playBeep("lock")
        sys.wait(vars.lock_wait_duration or 2000)
        PinModule.relayControl("Key1", 1)
        sys.wait(vars.lock_press_duration or 1000)
        PinModule.relayControl("Key1", 0)
        if not vars.key_state then 
            PinModule.relayControl("KeyPower", 0)
        end
    end)
end

function commands.unlockCommand()
    if not vars.isLicensed then 
        print("Device not licensed. Cannot execute unlockCommand")
        return 
    end
    
    -- Initialize key power
    PinModule.relayControl("KeyPower", 1)
    
    sys.taskInit(function()
        print("Unlock command executing")
        playBeep("unlock")
        sys.wait(vars.unlock_wait_duration or 1000)
        PinModule.relayControl("Key2", 1)
        sys.wait(vars.unlock_press_duration or 1000)
        PinModule.relayControl("Key2", 0)
        if not vars.key_state then 
            PinModule.relayControl("KeyPower", 0)
        end
    end)
end

function commands.asCommand()
    -- Initialize key for immobilizer recognition (Air780 uses KeyPower instead of pmd.ldoset)
    local success = pcall(PinModule.relayControl, "KeyPower", 1) -- Air780 equivalent
    if not success then
        print("Error in KeyPower control during asCommand")
        return
    end

    -- Check if the device is licensed
    if not vars.isLicensed then
        print("Device not licensed. Cannot execute asCommand")
        playBeep("untar") -- Play error sound
        return
    end

    -- Ensure all timing variables have default values if they're nil
    if not vars.lock_init_duration then vars.lock_init_duration = 2000 end
    if not vars.lock_press_duration then vars.lock_press_duration = 1000 end
    if not vars.lock_wait_duration then vars.lock_wait_duration = 2000 end
    if not vars.between_press_duration then vars.between_press_duration = 1000 end
    if not vars.remote_start_duration then vars.remote_start_duration = 4000 end
    if not vars.relay1_on_duration then vars.relay1_on_duration = 3000 end
    if not vars.relay2_on_duration then vars.relay2_on_duration = 3000 end
    if not vars.relay3_on_duration then vars.relay3_on_duration = 3000 end

    vars.carAlreadyStarted = true

    -- Move yielding part into a coroutine-safe context
    sys.taskInit(function()
        -- Check if we're in Geely Atlas mode
        if vars.geely_atlas_mode then
            print("Using Geely Atlas sequence for asCommand")
            sys.wait(vars.lock_init_duration)  -- wait some for key init
            print("Remote start button press")
            playBeep("as") -- Play sound 
            
            -- First lock sequence
            print("First lock press")
            success = pcall(PinModule.relayControl, "Key1", 1)
            if not success then
                print("Error in Key1 control (ON)")
                return
            end

            sys.wait(vars.lock_press_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Key1", 0)
            if not success then
                print("Error in Key1 control (OFF)")
                return
            end

            sys.wait(vars.between_press_duration)  -- Use configurable duration

            -- Second lock sequence
            print("Second lock press")
            success = pcall(PinModule.relayControl, "Key1", 1)
            if not success then
                print("Error in Key1 control (ON)")
                return
            end

            sys.wait(vars.lock_press_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Key1", 0)
            if not success then
                print("Error in Key1 control (OFF)")
                return
            end

            sys.wait(vars.between_press_duration)  -- Use configurable duration

            -- Remote start sequence using Relay2 (Air780 mapping)
            success = pcall(PinModule.relayControl, "Relay2", 1)
            if not success then
                print("Error in Relay2 control (ON)")
                return
            end

            vars.relay2_state = 1
            print("RELAY2 ON")

            sys.wait(vars.remote_start_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Relay2", 0)
            if not success then
                print("Error in Relay2 control (OFF)")
                return
            end

            vars.relay2_state = 0
            print("RELAY2 OFF")

            vars.as_wait_s2_falling = true
        else
            -- Standard sequence for other vehicles (Air780 mapping: Relay1->Relay3, Relay2->Relay2)
            playBeep("as") -- Play sound
            sys.wait(vars.lock_press_duration)  -- Use configurable duration
            sys.wait(vars.lock_wait_duration) -- Wait for configurable duration before proceeding
            
            vars.relay1_state = 1  -- This will be Relay3 on Air780
            PinModule.relayControl("Relay3", 1) -- Air780: Relay1 becomes Relay3
            print("RELAY3 ON")
            sys.wait(vars.relay1_on_duration) -- Wait for configurable duration
            
            vars.relay2_state = 1
            PinModule.relayControl("Relay2", 1) -- Air780: Relay2 stays Relay2
            print("RELAY2 ON")
            sys.wait(vars.relay2_on_duration) -- Wait for configurable duration
            
            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0) -- Turn off Relay 2
            vars.relay1_state = 0
            PinModule.relayControl("Relay3", 0) -- Turn off Relay 3 (was Relay1)
            vars.as_wait_s2_falling = true

            if vars.key_state == false then
                PinModule.relayControl("KeyPower", 0) -- Air780: Turn off key power instead of pmd.ldoset
            end
        end

        -- Set up auto-shutdown timer if enabled
        if vars.auto_shutdown_enabled then
            -- Record the time of the 'as' command
            vars.last_as_command_time = os.time()

            -- Cancel any existing auto-shutdown timer
            if vars.auto_shutdown_timer_id then
                sys.timerStop(vars.auto_shutdown_timer_id)
                vars.auto_shutdown_timer_id = nil
            end

            -- Set up a new auto-shutdown timer with the configurable duration
            vars.auto_shutdown_timer_id = sys.timerStart(function()
                -- Use pcall to catch any errors in the auto-shutdown function
                local success, err = pcall(function()
                    print("Auto-shutdown check - Current time:", os.time())

                    -- Only shut down if voltage indicates the car is still running
                    -- For Air780, we need to read voltage using ADC
                    local voltage = 12.0  -- Default voltage

                    -- Try to read actual voltage using ADC
                    if adc and adc.read then
                        local adcval, voltage_mv = adc.read(1, 0)  -- Using ADC config from main.lua
                        if voltage_mv then
                            voltage = (voltage_mv * 16.14) / 1000.0 + (vars.voltage_offset or 0)
                        end
                    end
                    
                    if voltage >= 13.5 then
                        print(string.format("Auto-shutdown triggered after %d minutes. Current voltage: %s",
                            vars.auto_shutdown_minutes, voltage))

                        -- Execute the untarCommand
                        local untar_success, untar_err = pcall(commands.untarCommand, 2000)
                        if not untar_success then
                            print("Failed to execute untar command: " .. (untar_err or "Unknown error"))
                        end

                        -- Send MQTT notification about auto-shutdown
                        if mqtt_module.is_ready() then
                            local success = mqtt_module.publish(string.format('{"status":"Auto-shutdown executed after %d minutes of inactivity"}', vars.auto_shutdown_minutes))
                            if not success then
                                print("Failed to send auto-shutdown notification")
                            end
                        end
                    else
                        print("Auto-shutdown canceled - car appears to be off already. Voltage: " .. voltage)
                    end
                end)

                if not success then
                    print("Error in auto-shutdown timer: " .. (err or "Unknown error"))
                end

                -- Clear the timer ID
                vars.auto_shutdown_timer_id = nil
            end, vars.auto_shutdown_time)

            print(string.format("Auto-shutdown timer set for %d minutes", vars.auto_shutdown_minutes))
        end

        -- Send MQTT notification about the asCommand execution
        if mqtt_module.is_ready() then
            local success = mqtt_module.publish('{"status":"as_command_executed"}')
            if not success then
                print("Failed to send MQTT notification")
            end
        end
    end)
end

function commands.untarCommand(duration)
    -- Check if the device is licensed
    if not vars.isLicensed then
        print("Device not licensed. Cannot execute untarCommand")
        playBeep("untar") -- Play error sound
        -- Send MQTT notification about license issue
        if mqtt_module.is_ready() then
            local success = mqtt_module.publish('{"status":"license_required"}')
            if not success then
                print("Failed to send license notification")
            end
        end
        return
    end

    -- Cancel any existing auto-shutdown timer
    if vars.auto_shutdown_timer_id then
        print("Canceling auto-shutdown timer due to manual untar command")
        sys.timerStop(vars.auto_shutdown_timer_id)
        vars.auto_shutdown_timer_id = nil
    end

    -- Initialize key for immobilizer recognition (Air780 uses KeyPower instead of pmd.ldoset)
    local success = pcall(PinModule.relayControl, "KeyPower", 1) -- Air780 equivalent
    if not success then
        print("Error in KeyPower control during untarCommand")
        return
    end

    -- Check if we're in Geely Atlas mode
    if vars.geely_atlas_mode then
        print("Using Geely Atlas sequence for untarCommand")

        sys.taskInit(function()
            sys.wait(2000)  -- Hold for 2 seconds to ensure engine stops
            playBeep("untar")
            vars.relay2_state = 1
            PinModule.relayControl("Relay2", 1)  -- Direct call since this may yield
            sys.wait(1000)  -- Hold for 1 second
            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0)  -- Direct call since this may yield
            sys.wait(1000)  -- Hold for 1 second
            vars.relay2_state = 1
            PinModule.relayControl("Relay2", 1)  -- Direct call since this may yield
            sys.wait(1000)  -- Hold for 1 second
            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0)  -- Direct call since this may yield
            sys.wait(2000)  -- Hold for 2 seconds to ensure engine stops
            -- Power down if needed (Air780: use KeyPower instead of pmd.ldoset)
            if vars.key_state == false then
                PinModule.relayControl("KeyPower", 0)
            end
            vars.carAlreadyStarted = false
        end)
    else
        -- Standard sequence for other vehicles
        playBeep("untar")
        vars.relay2_state = 1
        PinModule.relayControl("Relay2", 1)  -- Direct call since this may yield

        sys.taskInit(function()
            sys.wait(duration or 2000)

            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0)  -- Direct call since this may yield
            vars.carAlreadyStarted = false
        end)
    end
end

-- Function to enable Geely Atlas mode
function commands.enableGeelyMode()
    vars.geely_atlas_mode = true
    print("Geely Atlas mode enabled")

    -- Send MQTT notification
    if mqtt_module.is_ready() then
        local success = mqtt_module.publish('{"status":"geely_atlas_mode_enabled"}')
        if not success then
            print("Failed to send Geely mode notification")
        end
    end
end

-- Function to disable Geely Atlas mode
function commands.disableGeelyMode()
    vars.geely_atlas_mode = false
    print("Geely Atlas mode disabled")

    -- Send MQTT notification
    if mqtt_module.is_ready() then
        local success = mqtt_module.publish('{"status":"geely_atlas_mode_disabled"}')
        if not success then
            print("Failed to send Geely mode notification")
        end
    end
end

-- Function to get current Geely Atlas mode status
function commands.getGeelyModeStatus()
    return vars.geely_atlas_mode
end

-- Function to set timing parameters
function commands.setTimingParameter(param_name, value_ms)
    if param_name == "lock_press" then
        vars.lock_press_duration = value_ms
        print("Lock press duration set to " .. value_ms .. "ms")
    elseif param_name == "unlock_press" then
        vars.unlock_press_duration = value_ms
        print("Unlock press duration set to " .. value_ms .. "ms")
    elseif param_name == "lock_wait" then
        vars.lock_wait_duration = value_ms
        print("Lock wait duration set to " .. value_ms .. "ms")
    elseif param_name == "unlock_wait" then
        vars.unlock_wait_duration = value_ms
        print("Unlock wait duration set to " .. value_ms .. "ms")
    elseif param_name == "between_press" then
        vars.between_press_duration = value_ms
        print("Between press duration set to " .. value_ms .. "ms")
    elseif param_name == "remote_start" then
        vars.remote_start_duration = value_ms
        print("Remote start duration set to " .. value_ms .. "ms")
    elseif param_name == "relay1_on" then
        vars.relay1_on_duration = value_ms
        print("Relay1 on duration set to " .. value_ms .. "ms")
    elseif param_name == "relay2_on" then
        vars.relay2_on_duration = value_ms
        print("Relay2 on duration set to " .. value_ms .. "ms")
    elseif param_name == "relay3_on" then
        vars.relay3_on_duration = value_ms
        print("Relay3 on duration set to " .. value_ms .. "ms")
    elseif param_name == "lock_init" then
        vars.lock_init_duration = value_ms
        print("Lock init duration set to " .. value_ms .. "ms")
    else
        print("Unknown timing parameter: " .. param_name)
        return false
    end

    -- Send MQTT notification
    if mqtt_module.is_ready() then
        local success = mqtt_module.publish(string.format('{"status":"timing_parameter_set","parameter":"%s","value":%d}', param_name, value_ms))
        if not success then
            print("Failed to send timing parameter notification")
        end
    end

    return true
end

-- Function to get all timing parameters
function commands.getTimingParameters()
    local params = {
        lock_press = vars.lock_press_duration,
        unlock_press = vars.unlock_press_duration,
        lock_wait = vars.lock_wait_duration,
        unlock_wait = vars.unlock_wait_duration,
        between_press = vars.between_press_duration,
        remote_start = vars.remote_start_duration,
        relay1_on = vars.relay1_on_duration,
        relay2_on = vars.relay2_on_duration,
        relay3_on = vars.relay3_on_duration,
        lock_init = vars.lock_init_duration
    }

    return params
end

-- Function to read voltage (Air780 implementation)
function commands.readVoltage()
    if adc and adc.read then
        local adcval, voltage_mv = adc.read(1, 0)  -- Using ADC config from main.lua
        if voltage_mv then
            return (voltage_mv * 16.14) / 1000.0 + (vars.voltage_offset or 0)
        end
    end
    return 12.0  -- Default voltage if ADC read fails
end

-- Function to set voltage offset
function commands.setVoltageOffset(offset)
    if type(offset) == "number" then
        vars.voltage_offset = offset
        print(string.format("Voltage offset set to %.2f", offset))

        -- Send MQTT notification
        if mqtt_module.is_ready() then
            local success = mqtt_module.publish(string.format('{"status":"voltage_offset_set","value":%.2f}', offset))
            if not success then
                print("Failed to send voltage offset notification")
            end
        end
        return true
    end
    return false
end

-- Function to set voltage threshold
function commands.setVoltageThreshold(threshold)
    if type(threshold) == "number" and threshold > 0 then
        vars.voltage_threshold = threshold
        print(string.format("Voltage threshold set to %.2f", threshold))

        -- Send MQTT notification
        if mqtt_module.is_ready() then
            local success = mqtt_module.publish(string.format('{"status":"voltage_threshold_set","value":%.2f}', threshold))
            if not success then
                print("Failed to send voltage threshold notification")
            end
        end
        return true
    end
    return false
end

-- Function to enable voltage notifications
function commands.enableVoltageNotifications()
    vars.voltage_notify_flag = true
    print("Voltage notifications enabled")

    -- Send MQTT notification
    if mqtt_module.is_ready() then
        local success = mqtt_module.publish('{"status":"voltage_notifications_enabled"}')
        if not success then
            print("Failed to send voltage notification status")
        end
    end
end

-- Function to disable voltage notifications
function commands.disableVoltageNotifications()
    vars.voltage_notify_flag = false
    print("Voltage notifications disabled")

    -- Send MQTT notification
    if mqtt_module.is_ready() then
        local success = mqtt_module.publish('{"status":"voltage_notifications_disabled"}')
        if not success then
            print("Failed to send voltage notification status")
        end
    end
end

-- Function to get voltage status
function commands.getVoltageStatus()
    local status = {
        current_voltage = commands.readVoltage(),
        voltage_offset = vars.voltage_offset or 0,
        voltage_threshold = vars.voltage_threshold or 0.5,
        voltage_notify_flag = vars.voltage_notify_flag or false
    }

    return status
end

-- Function to check if voltage indicates car is running
function commands.isCarRunning()
    local voltage = commands.readVoltage()
    return voltage >= 13.5  -- Car is considered running if voltage >= 13.5V
end

-- Function to check if voltage indicates car is off
function commands.isCarOff()
    local voltage = commands.readVoltage()
    return voltage < 13.0  -- Car is considered off if voltage < 13.0V
end

return commands
